"""
Enhanced MCP Manager with Bedrock Session Management - FIXED FOR NOVA

Key fixes for Nova model compatibility:
1. Simplified tool schema to avoid complex nested structures
2. Added proper tool response formatting
3. Fixed tool name cleaning and mapping
4. Enhanced error handling for Nova-specific issues
"""

import logging
import os
import json
import html
import asyncio
from typing import Dict, List, Any, Optional
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """
    Mixin class to add Bedrock session-aware functionality to existing MCPClientManager.
    Fixed for Nova model compatibility.
    """
    
    # Default; manager overrides from env
    model_id: str = "apac.amazon.nova-lite-v1:0"
    
    async def chat_with_bedrock_with_context(
        self,
        message: str,
        session_id: str,
        tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Enhanced Bedrock chat with native session context retention using session_manager.
        """
        try:
            chat_session = session_manager.get_or_create_session(session_id)

            # Check if session has already used too many tools
            session_tool_count = getattr(chat_session, 'total_tools_used', 0)
            max_session_tools = 25  # Global session limit

            if session_tool_count >= max_session_tools:
                logger.warning(f"Session {session_id} has already used {session_tool_count} tools (limit: {max_session_tools})")
                return {
                    "response": "This conversation has reached the maximum number of tool uses. Please start a new conversation to continue using tools.",
                    "tools_used": [],
                    "session_id": session_id
                }

            historical_messages = chat_session.get_bedrock_messages(max_turns=8)
            
            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]
            
            system_message = self._build_context_aware_system_message(chat_session, tools_available)
            tool_config = self._build_tool_config_for_bedrock(tools_available)
            
            result = await self._execute_contextual_conversation(
                messages=current_messages,
                system_message=system_message,
                tool_config=tool_config,
                session_id=session_id,
                model_id=self.model_id,
            )
            
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", []),
            )
            
            tools_used_count = len(result.get('tools_used', []))
            logger.info(f"Completed contextual chat for session {session_id}: {tools_used_count} tools used")

            # Update session tool count tracking
            if hasattr(chat_session, 'total_tools_used'):
                chat_session.total_tools_used += tools_used_count
            else:
                chat_session.total_tools_used = tools_used_count
            
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error in contextual chat for session {session_id}: {error_msg}")
            
            # Enhanced error handling
            if "ModelErrorException" in error_msg and "invalid sequence" in error_msg:
                response_text = "I encountered an issue with tool formatting. Let me try to provide a response without using tools."
            elif "Too many tools used" in error_msg:
                response_text = "I've reached the limit for tool usage in this conversation. Let me provide a response based on the information I have."
            elif "ThrottlingException" in error_msg or "Too many tokens" in error_msg:
                response_text = "I'm currently experiencing rate limits. Please wait a moment before continuing."
            elif "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Please check your BEDROCK_MODEL_ID in .env file. Current model: {self.model_id}"
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Please check your AWS_REGION configuration. Current region: {os.getenv('AWS_REGION', 'not set')}"
                else:
                    response_text = f"Bedrock API validation error: {error_msg}"
            else:
                response_text = f"I apologize, but I encountered an error: {error_msg}"
            
            return {
                "response": response_text,
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def _build_context_aware_system_message(
        self,
        chat_session,
        tools_available: Optional[List[str]] = None
    ) -> str:
        """Build context-aware system message optimized for Nova."""
        context = ""
        if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
            context = chat_session.get_context_for_bedrock()
        
        tool_hint = ""
        if tools_available:
            # Simplified tool instruction for Nova
            tool_hint = (
                "\nYou have access to tools. Use them when needed to get real data. "
                "Call tools one at a time and wait for results before proceeding."
            )
        
        system = (
            "You are an AWS Infrastructure Monitoring Assistant. Help users understand their AWS infrastructure and costs.\n\n"
            "Key principles:\n"
            "- Use available tools to get real data\n"
            "- Never fabricate information\n"
            "- Provide clear, helpful responses\n"
            "- Default region: ap-south-1\n"
            f"{context}"
            f"{tool_hint}"
        )
        
        return system

    def _simplify_schema_for_nova(self, schema: dict) -> dict:
        """Simplify schema to basic types that Nova can handle reliably."""
        if not isinstance(schema, dict):
            return {"type": "object", "properties": {}}

        simplified = {
            "type": schema.get("type", "object"),
            "properties": {}
        }

        if "properties" in schema and isinstance(schema["properties"], dict):
            for prop_name, prop_def in schema["properties"].items():
                if isinstance(prop_def, dict):
                    # Simplify to basic string type for Nova
                    # Nova handles string inputs better than complex types
                    simplified["properties"][prop_name] = {
                        "type": "string",
                        "description": prop_def.get("description", f"Parameter {prop_name}")
                    }

        # Add required fields if present
        if "required" in schema and isinstance(schema["required"], list):
            simplified["required"] = schema["required"]

        return simplified

    def _build_tool_config_for_bedrock(self, tools_available: Optional[List[str]] = None) -> Optional[Dict]:
        """Build Nova-compliant tool configuration for Bedrock."""
        if not tools_available:
            return None

        available_tools = self.get_available_tools()
        tools: List[Dict[str, Any]] = []
        
        # Initialize the mapping
        self._tool_name_mapping = {}

        for tool_key in tools_available:
            if tool_key not in available_tools:
                logger.warning(f"Tool {tool_key} not in available tools")
                continue

            tool_data = available_tools[tool_key]
            tool = tool_data["tool"]

            # Simplify schema for Nova
            input_schema = tool.get("input_schema", {})
            nova_schema = self._simplify_schema_for_nova(input_schema)

            # Clean tool name for Nova (alphanumeric + underscores only)
            clean_tool_name = ''.join(c if c.isalnum() or c == '_' else '_' for c in tool_key)
            
            # Ensure it starts with a letter
            if clean_tool_name and not clean_tool_name[0].isalpha():
                clean_tool_name = f"tool_{clean_tool_name}"
            
            # Store mapping for later lookup
            self._tool_name_mapping[clean_tool_name] = tool_key

            tools.append({
                "toolSpec": {
                    "name": clean_tool_name,
                    "description": tool.get("description", f"Tool: {tool_key}"),
                    "inputSchema": {"json": nova_schema}
                }
            })

        if not tools:
            return None

        # Nova works best with auto tool choice
        return {
            "tools": tools,
            "toolChoice": {"auto": {}}
        }

    async def _execute_contextual_conversation(
        self,
        messages: List[Dict[str, Any]],
        system_message: str,
        tool_config: Optional[Dict],
        session_id: str,
        model_id: str,
    ) -> Dict[str, Any]:
        """Execute contextual conversation with improved Nova tool handling."""
        runtime = await self.get_async_bedrock_runtime()
        
        # Nova-optimized inference config
        inference_config = {
            "temperature": 0.1,      # Small non-zero value works better than 0
            "topP": 0.9,
            "maxTokens": 2048
        }
        
        msgs = list(messages)
        tools_used: List[Dict[str, Any]] = []
        max_iterations = 5  # Reduced to prevent issues
        max_tools_per_session = 10  # Conservative limit
        
        for iteration in range(max_iterations):
            # Check tool usage limits
            if len(tools_used) >= max_tools_per_session:
                logger.warning(f"Stopping: reached max tools limit ({max_tools_per_session})")
                final_response = "I've gathered the requested information. Let me summarize what I found."
                return {"response": final_response, "tools_used": tools_used, "session_id": session_id}

            try:
                req = {
                    "modelId": model_id,
                    "messages": msgs,
                    "system": [{"text": system_message}],
                    "inferenceConfig": inference_config,
                }

                if tool_config:
                    req["toolConfig"] = tool_config

                logger.debug(f"Sending request to model: {model_id}")
                
                # Add small delay between iterations to prevent rate limiting
                if iteration > 0:
                    await asyncio.sleep(0.3)
                
                resp = await runtime.converse(**req)
                output = resp.get("output", {}).get("message", {})
                content = output.get("content", [])
                stop_reason = resp.get("stopReason")
                
                logger.debug(f"Iteration {iteration + 1}: stop_reason={stop_reason}")
                
                if stop_reason == "tool_use":
                    # Record assistant's tool use message
                    msgs.append({"role": "assistant", "content": content})
                    
                    # Process tool calls
                    tool_results = []
                    for block in content:
                        if "toolUse" in block:
                            tool_use = block["toolUse"]
                            tool_use_id = tool_use.get("toolUseId")
                            tool_name = tool_use.get("name")
                            tool_input = tool_use.get("input", {})
                            
                            # Get original tool key from mapping
                            original_key = self._tool_name_mapping.get(tool_name, tool_name)
                            
                            logger.info(f"Executing tool: {original_key}")
                            
                            # Process tool input
                            processed_input = self._process_tool_input(tool_input, original_key)
                            
                            # Execute tool
                            result = await self._execute_single_tool(
                                original_key, 
                                processed_input, 
                                tool_use_id,
                                session_id
                            )
                            
                            tools_used.append(result)
                            
                            # Format tool result for model
                            if result.get("success"):
                                result_text = self._format_tool_result(result.get("result", ""))
                            else:
                                result_text = f"Error: {result.get('error', 'Unknown error')}"
                            
                            tool_results.append({
                                "toolResult": {
                                    "toolUseId": tool_use_id,
                                    "content": [{"text": result_text}]
                                }
                            })
                    
                    # Add tool results as user message
                    if tool_results:
                        msgs.append({"role": "user", "content": tool_results})
                    
                    continue
                
                else:
                    # Conversation complete
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else ""
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}
                    
            except Exception as e:
                error_str = str(e)
                logger.error(f"Error in iteration {iteration + 1}: {error_str}")
                
                # Check for specific Nova tool errors
                if "ModelErrorException" in error_str and "invalid sequence" in error_str:
                    logger.warning("Nova tool formatting error, attempting without tools")
                    # Remove tool config and try again
                    if tool_config and iteration == 0:
                        req.pop("toolConfig", None)
                        try:
                            resp = await runtime.converse(**req)
                            output = resp.get("output", {}).get("message", {})
                            content = output.get("content", [])
                            final_text_parts = [b["text"] for b in content if "text" in b]
                            final_text = "\n".join(final_text_parts).strip()
                            return {"response": final_text, "tools_used": [], "session_id": session_id}
                        except:
                            pass
                
                if iteration == 0:
                    raise
                
                # Return partial response
                return {
                    "response": f"I encountered an issue: {error_str}",
                    "tools_used": tools_used,
                    "session_id": session_id
                }
        
        # Max iterations reached
        return {
            "response": "I've completed gathering the information you requested.",
            "tools_used": tools_used,
            "session_id": session_id
        }

    def _process_tool_input(self, tool_input: dict, tool_key: str) -> dict:
        """Process and clean tool input for execution."""
        if not isinstance(tool_input, dict):
            return {}

        processed = {}

        for key, value in tool_input.items():
            # Handle string values that might be JSON
            if isinstance(value, str):
                # Decode HTML entities
                value = html.unescape(value)

                # Try to parse as JSON if it looks like JSON
                stripped_value = value.strip()
                if stripped_value.startswith('{') or stripped_value.startswith('['):
                    try:
                        value = json.loads(stripped_value)
                    except json.JSONDecodeError:
                        # Try to handle single-quoted JSON (common with Nova)
                        try:
                            import ast
                            # ast.literal_eval can handle both single and double quotes
                            value = ast.literal_eval(stripped_value)
                        except:
                            # If that fails, try manual conversion for simple cases
                            try:
                                if stripped_value.startswith("'{") and stripped_value.endswith("}'"):
                                    json_str = stripped_value[1:-1]  # Remove outer single quotes
                                    # Replace single quotes around keys/values with double quotes
                                    import re
                                    # Replace single quotes around word characters, but be careful with commas
                                    json_str = re.sub(r"'(\w+)'(\s*:)", r'"\1"\2', json_str)  # keys
                                    json_str = re.sub(r"(\w+)'\s*([,\}])", r'"\1"\2', json_str)  # values at end
                                    json_str = re.sub(r"'([^']+)'", r'"\1"', json_str)  # other quoted strings
                                    value = json.loads(json_str)
                            except:
                                pass

            processed[key] = value

        # Special handling for Cost Explorer tools
        if 'get_cost_and_usage' in tool_key:
            processed = self._transform_cost_explorer_params(processed, tool_key)

        return processed

    def _format_tool_result(self, result: Any) -> str:
        """Format tool result for model consumption."""
        if isinstance(result, (dict, list)):
            # Convert to JSON with limited depth to avoid overwhelming the model
            return json.dumps(result, indent=2, default=str)[:4000]
        return str(result)[:4000]

    async def _execute_single_tool(
        self, 
        tool_key: str, 
        tool_input: dict, 
        tool_use_id: str,
        session_id: str
    ) -> dict:
        """Execute a single tool call with error handling."""
        try:
            # Parse tool key
            server_name, tool_name = self._parse_tool_key(tool_key)
            
            # Call the tool
            result = await self.call_tool(server_name, tool_name, tool_input)
            
            return {
                "tool_name": tool_name,
                "server_name": server_name,
                "input": tool_input,
                "success": result.get("success", False),
                "result": result.get("result", ""),
                "error": result.get("error", None),
                "session_id": session_id,
                "toolUseId": tool_use_id,
            }
            
        except Exception as e:
            logger.error(f"Tool execution error for {tool_key}: {e}")
            return {
                "tool_name": tool_key,
                "server_name": "unknown",
                "input": tool_input,
                "success": False,
                "error": str(e),
                "session_id": session_id,
                "toolUseId": tool_use_id,
            }

    def _filter_thinking_content(self, text: str) -> str:
        """Filter out thinking/reasoning content from model responses."""
        import re
        
        if not text:
            return text
        
        # Remove various thinking/reasoning tags
        patterns = [
            r'<thinking>.*?</thinking>',
            r'<reasoning>.*?</reasoning>',
            r'<analysis>.*?</analysis>',
            r'<thought>.*?</thought>',
        ]
        
        filtered_text = text
        for pattern in patterns:
            filtered_text = re.sub(pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)
        
        # Clean up whitespace
        filtered_text = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_text)
        return filtered_text.strip()

    async def get_async_bedrock_runtime(self):
        """Get async bedrock runtime client."""
        try:
            import aioboto3
        except ImportError:
            raise ImportError("aioboto3 is required. Install with: pip install aioboto3")
        
        session = aioboto3.Session()
        client = session.client("bedrock-runtime")
        return await client.__aenter__()

    def _parse_tool_key(self, tool_key: str) -> tuple[str, str]:
        """Parse tool key to get server and tool name."""
        if not tool_key:
            raise ValueError("Tool key is empty")
        
        available_tools = self.get_available_tools()
        
        # Direct lookup
        if tool_key in available_tools:
            tool_data = available_tools[tool_key]
            return tool_data["server"], tool_data["tool"]["name"]
        
        # Try separator parsing
        if "::" in tool_key:
            parts = tool_key.split("::", 1)
            if len(parts) == 2:
                return parts[0], parts[1]
        
        # Search by tool name
        for key, tool_data in available_tools.items():
            if tool_data["tool"]["name"] == tool_key:
                return tool_data["server"], tool_key
        
        raise ValueError(f"Tool '{tool_key}' not found")

    def _transform_cost_explorer_params(self, tool_input: dict, tool_name: str) -> dict:
        """Transform parameters for Cost Explorer API compliance."""
        if not 'get_cost_and_usage' in tool_name:
            return tool_input

        transformed = tool_input.copy()

        # Handle various date parameter formats
        if 'date_range' in tool_input:
            date_range = tool_input['date_range']
            start_date, end_date = self._parse_date_range(date_range)

            if start_date and end_date:
                # Keep as date_range object for the MCP tool
                transformed['date_range'] = {
                    'start_date': start_date,
                    'end_date': end_date
                }

        elif 'start_date' in tool_input and 'end_date' in tool_input:
            start_date = self._clean_date_string(tool_input['start_date'])
            end_date = self._clean_date_string(tool_input['end_date'])

            if start_date and end_date:
                # Convert to date_range object for the MCP tool
                transformed.pop('start_date', None)
                transformed.pop('end_date', None)
                transformed['date_range'] = {
                    'start_date': start_date,
                    'end_date': end_date
                }

        return transformed

    def _parse_date_range(self, date_range: Any) -> tuple[str, str]:
        """Parse date range from various formats."""
        if isinstance(date_range, dict):
            return (
                self._clean_date_string(date_range.get('start_date', '')),
                self._clean_date_string(date_range.get('end_date', ''))
            )

        # Convert to string and clean
        date_str = str(date_range)
        date_str = html.unescape(date_str)

        # Try to parse as JSON first
        try:
            parsed = json.loads(date_str)
            if isinstance(parsed, dict):
                return (
                    self._clean_date_string(parsed.get('start_date', '')),
                    self._clean_date_string(parsed.get('end_date', ''))
                )
        except:
            pass

        # Try to handle HTML-encoded JSON that might be wrapped in quotes
        if date_str.startswith("'") and date_str.endswith("'"):
            try:
                # Remove outer quotes and try to parse as JSON
                inner_str = date_str[1:-1]
                parsed = json.loads(inner_str)
                if isinstance(parsed, dict):
                    return (
                        self._clean_date_string(parsed.get('start_date', '')),
                        self._clean_date_string(parsed.get('end_date', ''))
                    )
            except:
                pass

        # Handle Nova's common string formats
        # Format: "'2025-08-01','2025-08-31'"
        if date_str.startswith("'") and "'," in date_str:
            try:
                # Remove outer quotes and split on ','
                cleaned = date_str.strip("'\"")
                if "'," in cleaned:
                    parts = cleaned.split("',", 1)
                    start_date = parts[0].strip("'\"")
                    end_date = parts[1].strip("'\"")
                    return (
                        self._clean_date_string(start_date),
                        self._clean_date_string(end_date)
                    )
            except:
                pass

        # Try comma-separated without quotes
        if ',' in date_str:
            parts = date_str.split(',', 1)
            return (
                self._clean_date_string(parts[0]),
                self._clean_date_string(parts[1])
            )

        return ('', '')

    def _clean_date_string(self, date_str: str) -> str:
        """Clean and validate date string to YYYY-MM-DD format."""
        if not date_str:
            return ''

        # Clean the string
        cleaned = str(date_str).strip()
        cleaned = html.unescape(cleaned)
        cleaned = cleaned.strip("'\"").strip()

        # Check if already in correct format
        import re
        if re.match(r'^\d{4}-\d{2}-\d{2}$', cleaned):
            return cleaned

        # Try to convert other formats
        # Add conversion logic here if needed

        return cleaned

    def _is_valid_date_string(self, date_str: str) -> bool:
        """Validate if a string is in YYYY-MM-DD format."""
        if not date_str or not isinstance(date_str, str):
            return False

        import re
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
            return False

        # Additional validation for reasonable date ranges
        try:
            year, month, day = map(int, date_str.split('-'))
            if year < 2000 or year > 2030:  # Reasonable range for AWS costs
                return False
            if month < 1 or month > 12:
                return False
            if day < 1 or day > 31:
                return False
            return True
        except ValueError:
            return False
