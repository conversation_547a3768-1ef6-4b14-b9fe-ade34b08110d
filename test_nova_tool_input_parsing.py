#!/usr/bin/env python3
"""
Test Nova tool input parsing fixes
"""

import json
import html
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_tool_input_parsing():
    """Test the tool input parsing logic for Nova models."""
    
    # Test cases that Nova might produce
    test_cases = [
        # Case 1: Normal JSON object (should work as-is)
        {
            "input": {"start_date": "2025-09-01", "end_date": "2025-09-30"},
            "expected": {"start_date": "2025-09-01", "end_date": "2025-09-30"},
            "description": "Normal JSON object"
        },
        
        # Case 2: HTML-encoded JSON string (Nova issue)
        {
            "input": "&#39;{\"start_date\": \"2025-09-01\", \"end_date\": \"2025-09-30\"}&#39;",
            "expected": {"start_date": "2025-09-01", "end_date": "2025-09-30"},
            "description": "HTML-encoded JSON string"
        },
        
        # Case 3: Simple JSON string
        {
            "input": "{\"start_date\": \"2025-09-01\", \"end_date\": \"2025-09-30\"}",
            "expected": {"start_date": "2025-09-01", "end_date": "2025-09-30"},
            "description": "Simple JSON string"
        },
        
        # Case 4: Invalid JSON string (should fallback to empty dict)
        {
            "input": "invalid json string",
            "expected": {},
            "description": "Invalid JSON string"
        },
        
        # Case 5: Empty input
        {
            "input": {},
            "expected": {},
            "description": "Empty input"
        }
    ]
    
    def parse_tool_input(tool_input):
        """Replicate the parsing logic from enhanced_mcp_manager.py"""
        # If input is a string (Nova sometimes does this), try to parse it as JSON
        if isinstance(tool_input, str):
            try:
                # Decode HTML entities first
                decoded_input = html.unescape(tool_input)

                # Remove outer quotes if present (Nova sometimes adds them)
                if decoded_input.startswith("'") and decoded_input.endswith("'"):
                    decoded_input = decoded_input[1:-1]
                elif decoded_input.startswith('"') and decoded_input.endswith('"'):
                    decoded_input = decoded_input[1:-1]

                # Try to parse as JSON
                tool_input = json.loads(decoded_input)
                logger.debug(f"Parsed string input as JSON: {tool_input}")
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse tool input as JSON: {tool_input}, error: {e}")
                # If parsing fails, try to extract key-value pairs
                tool_input = {}

        return tool_input
    
    print("🧪 Testing Nova tool input parsing...")
    
    all_passed = True
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['description']}")
        print(f"Input: {test_case['input']}")
        
        try:
            result = parse_tool_input(test_case['input'])
            print(f"Result: {result}")
            print(f"Expected: {test_case['expected']}")
            
            if result == test_case['expected']:
                print("✅ PASSED")
            else:
                print("❌ FAILED")
                all_passed = False
                
        except Exception as e:
            print(f"❌ FAILED with exception: {e}")
            all_passed = False
    
    print(f"\n{'='*50}")
    if all_passed:
        print("✅ All tests PASSED! Nova tool input parsing is working correctly.")
    else:
        print("❌ Some tests FAILED. Check the parsing logic.")
    
    return all_passed

def test_html_decoding():
    """Test HTML entity decoding specifically."""
    print("\n🧪 Testing HTML entity decoding...")
    
    test_cases = [
        ("&#39;hello&#39;", "'hello'"),
        ("&quot;test&quot;", '"test"'),
        ("&amp;", "&"),
        ("&lt;tag&gt;", "<tag>"),
        ("normal text", "normal text"),
    ]
    
    for encoded, expected in test_cases:
        result = html.unescape(encoded)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{encoded}' -> '{result}' (expected: '{expected}')")

if __name__ == "__main__":
    print("Testing Nova tool input parsing fixes...")
    
    # Test HTML decoding
    test_html_decoding()
    
    # Test tool input parsing
    success = test_tool_input_parsing()
    
    if success:
        print("\n🎉 All Nova tool input parsing tests passed!")
    else:
        print("\n💥 Some tests failed. Check the implementation.")
