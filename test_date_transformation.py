#!/usr/bin/env python3
"""
Test the date transformation logic for Cost Explorer parameters
"""

import sys
import os
sys.path.append('.')

from enhanced_mcp_manager import EnhancedMCPMixin
import json

def test_date_transformation():
    """Test the _transform_cost_explorer_params method with various inputs."""

    mixin = EnhancedMCPMixin()

    print("🧪 Testing Cost Explorer date parameter transformation...")

    # Test case 1: HTML-encoded comma-separated dates
    date_range_1 = r''2025-09-01','2025-09-30''
    input1 = {'date_range': date_range_1, 'metric': 'UnblendedCost', 'granularity': 'MONTHLY'}
    result1 = mixin._transform_cost_explorer_params(input1, 'get_cost_and_usage')
    print('\nTest 1 - HTML-encoded comma-separated dates:')
    print('Input date_range:', repr(date_range_1))
    print('Output:', result1)
    expected_timeperiod = {'Start': '2025-09-01', 'End': '2025-09-30'}
    if result1.get('TimePeriod') == expected_timeperiod:
        print('✅ PASS')
    else:
        print('❌ FAIL')

    # Test case 2: HTML-encoded JSON
    date_range_2 = r''{"start_date": "2025-09-01", "end_date": "2025-09-30"}''
    input2 = {'date_range': date_range_2, 'metric': 'UnblendedCost', 'granularity': 'MONTHLY'}
    result2 = mixin._transform_cost_explorer_params(input2, 'get_cost_and_usage')
    print('\nTest 2 - HTML-encoded JSON dates:')
    print('Input date_range:', repr(date_range_2))
    print('Output:', result2)
    if result2.get('TimePeriod') == expected_timeperiod:
        print('✅ PASS')
    else:
        print('❌ FAIL')

    # Test case 3: Direct start_date/end_date
    input3 = {'start_date': '2025-09-01', 'end_date': '2025-09-30', 'metric': 'UnblendedCost', 'granularity': 'MONTHLY'}
    result3 = mixin._transform_cost_explorer_params(input3, 'get_cost_and_usage')
    print('\nTest 3 - Direct start_date/end_date:')
    print('Input:', input3)
    print('Output:', result3)
    if result3.get('TimePeriod') == expected_timeperiod:
        print('✅ PASS')
    else:
        print('❌ FAIL')

    # Test case 4: Invalid input
    input4 = {'invalid_param': 'some_value', 'metric': 'UnblendedCost', 'granularity': 'MONTHLY'}
    result4 = mixin._transform_cost_explorer_params(input4, 'get_cost_and_usage')
    print('\nTest 4 - Invalid input (no date params):')
    print('Input:', input4)
    print('Output:', result4)
    if 'TimePeriod' not in result4:
        print('✅ PASS (no transformation expected)')
    else:
        print('❌ FAIL (unexpected transformation)')

    print('\n🎉 Date transformation testing completed!')

if __name__ == "__main__":
    test_date_transformation()