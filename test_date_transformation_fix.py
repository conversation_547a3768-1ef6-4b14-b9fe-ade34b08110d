#!/usr/bin/env python3
"""
Test script to verify the date transformation fix for Cost Explorer API.
This tests the specific issues that were causing the get_cost_and_usage tool failures.
"""

import sys
import os
import json
import logging

# Add the current directory to Python path to import the enhanced_mcp_manager
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from enhanced_mcp_manager import EnhancedMCPMixin

# Set up logging to see the debug output
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

class TestDateTransformation:
    """Test class to verify date transformation fixes."""

    def __init__(self):
        self.mixin = EnhancedMCPMixin()

    def test_problematic_inputs(self):
        """Test the specific inputs that were causing failures in the logs."""

        test_cases = [
            # Case 1: The HTML entity encoded string from the logs
            {
                "name": "HTML entity encoded dates",
                "input": {"date_range": "'2025-08-01','2025-08-31'"},
                "expected_start": "2025-08-01",
                "expected_end": "2025-08-31"
            },
            # Case 1.5: Actual HTML entities from error logs
            {
                "name": "Actual HTML entities from logs",
                "input": {"date_range": "'2025-09-01','2025-09-30'"},
                "expected_start": "2025-09-01",
                "expected_end": "2025-09-30"
            },
            # Case 2: JSON object format
            {
                "name": "JSON object format",
                "input": {"date_range": '{"start_date": "2025-08-01", "end_date": "2025-08-31"}'},
                "expected_start": "2025-08-01",
                "expected_end": "2025-08-31"
            },
            # Case 3: Comma-separated with quotes
            {
                "name": "Comma-separated with quotes",
                "input": {"date_range": "'2025-08-01','2025-08-31'"},
                "expected_start": "2025-08-01",
                "expected_end": "2025-08-31"
            },
            # Case 4: Comma-separated without quotes
            {
                "name": "Comma-separated without quotes",
                "input": {"date_range": "2025-08-01,2025-08-31"},
                "expected_start": "2025-08-01",
                "expected_end": "2025-08-31"
            },
            # Case 5: Direct start_date/end_date parameters
            {
                "name": "Direct date parameters",
                "input": {"start_date": "2025-08-01", "end_date": "2025-08-31"},
                "expected_start": "2025-08-01",
                "expected_end": "2025-08-31"
            }
        ]

        print("Testing date transformation fixes...")
        print("=" * 60)

        all_passed = True

        for i, test_case in enumerate(test_cases, 1):
            print(f"\nTest {i}: {test_case['name']}")
            print("-" * 40)

            try:
                # Transform the parameters
                transformed = self.mixin._transform_cost_explorer_params(
                    test_case['input'].copy(),
                    'get_cost_and_usage'
                )

                # Check if TimePeriod was created
                if 'TimePeriod' not in transformed:
                    print(f"❌ FAILED: No TimePeriod object created")
                    print(f"   Input: {test_case['input']}")
                    print(f"   Output: {transformed}")
                    all_passed = False
                    continue

                timeperiod = transformed['TimePeriod']
                start_date = timeperiod.get('Start')
                end_date = timeperiod.get('End')

                # Validate the results
                start_correct = start_date == test_case['expected_start']
                end_correct = end_date == test_case['expected_end']

                if start_correct and end_correct:
                    print(f"✅ PASSED: Start={start_date}, End={end_date}")
                    print(f"   Input: {test_case['input']}")
                    print(f"   TimePeriod type: {type(timeperiod)}")
                else:
                    print(f"❌ FAILED: Expected Start={test_case['expected_start']}, End={test_case['expected_end']}")
                    print(f"   Got Start={start_date}, End={end_date}")
                    print(f"   Input: {test_case['input']}")
                    all_passed = False

            except Exception as e:
                print(f"❌ ERROR: {e}")
                print(f"   Input: {test_case['input']}")
                all_passed = False

        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 ALL TESTS PASSED! The date transformation fix is working correctly.")
        else:
            print("❌ Some tests failed. The fix needs more work.")

        return all_passed

    def test_validation_methods(self):
        """Test the validation methods to ensure they work correctly."""
        print("\nTesting validation methods...")
        print("=" * 40)

        # Test valid dates
        valid_dates = ["2025-08-01", "2025-12-31", "2024-01-15"]
        for date in valid_dates:
            is_valid = self.mixin._is_valid_date_string(date)
            print(f"Date '{date}': {'✅ Valid' if is_valid else '❌ Invalid'}")

        # Test invalid dates
        invalid_dates = ["2025/08/01", "08-01-2025", "2025-13-01", "not-a-date"]
        for date in invalid_dates:
            is_valid = self.mixin._is_valid_date_string(date)
            print(f"Date '{date}': {'✅ Valid' if is_valid else '❌ Invalid'}")

if __name__ == "__main__":
    tester = TestDateTransformation()
    success = tester.test_problematic_inputs()
    tester.test_validation_methods()

    if success:
        print("\n🎉 Date transformation fix verification completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Date transformation fix verification failed!")
        sys.exit(1)